import React, { useState } from "react";
import { <PERSON><PERSON>View } from "@/components/ui/scroll-view";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";

import { Pressable } from "@/components/ui/pressable";
import { Box } from "@/components/ui/box";
import { Icon } from "@/components/ui/icon";
import {
  Heart,
  Clock,
  MapPin,
  Calendar,
  ArrowLeft,
  Share2,
  DoorClosed,
  BookUser,
} from "lucide-react-native";
import { ClassDetailsResponse } from "@/data/screens/classes/types";
import { ImageBackground, View, StatusBar, Share } from "react-native";
import { router } from "expo-router";
import {
  getInitials,
  obtainDateFrame,
  getRandomColorForInitials,
} from "@/data/common/common.utils";
import { obtainSpotsAvailable } from "@/data/screens/classes/utils";
import { differenceInMinutes, format, parse, parseISO } from "date-fns";
import { DATE_FORMAT } from "@/constants/date-formats";
import { TimerIcon } from "@/components/shared/icon/timer";
import { LowChartIcon } from "@/components/shared/icon/low-chart";
import { ClassStatusButton } from "../class-card/class-button";
import { getButtonStatus } from "../class-card/utils";
import { useReserveClass } from "@/data/screens/classes/mutations/useReserveClass";
import { ReservationSuccessActionsheet } from "../class-card/reservation-success-actionsheet";
import { addToCalendar, createShareText } from "@/utils/calendar";
import { useCancelReservation } from "@/data/screens/classes/mutations/useCancelReservation";
import { S } from "@expo/html-elements";

interface ClassDetailsProps {
  classItem: ClassDetailsResponse;
  selectedDate?: string;
  isLoading?: boolean;
}

export const ClassDetails: React.FC<ClassDetailsProps> = ({
  classItem,
  selectedDate,
  isLoading = false,
}) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [showSuccessActionsheet, setShowSuccessActionsheet] = useState(false);
  const statusBarHeight = StatusBar.currentHeight || 44; // Default to 44 for iOS

  const instructorName = `${classItem.instructor_first_name} ${classItem.instructor_last_name}`;
  const backgroundColor = getRandomColorForInitials(instructorName);

  const { mutate: reserveClass, isPending } = useReserveClass(() => {
    setShowSuccessActionsheet(true);
  });

  const { mutate: cancelReservation, isPending: isCancelling } =
    useCancelReservation();

  const handleMakeAnotherReservation = () => {
    setShowSuccessActionsheet(false);
    router.back(); // Navigate back to classes list
  };

  const handleAddToCalendar = async () => {
    setShowSuccessActionsheet(false);

    await addToCalendar({
      title: classItem.name,
      startTime: classItem.start_time,
      endTime: classItem.end_time,
      date: selectedDate ?? "",
      location: `${classItem.gym_name}, ${classItem.room_name}`,
      description: `Fitness class with ${classItem.instructor_first_name} ${classItem.instructor_last_name}`,
      instructor: `${classItem.instructor_first_name} ${classItem.instructor_last_name}`,
    });
  };

  const handleShareWithFriends = () => {
    setShowSuccessActionsheet(false);

    const shareText = createShareText({
      title: classItem.name,
      startTime: classItem.start_time,
      endTime: classItem.end_time,
      date: selectedDate ?? "",
      location: `${classItem.gym_name}, ${classItem.room_name}`,
      instructor: `${classItem.instructor_first_name} ${classItem.instructor_last_name}`,
    });

    Share.share({
      message: shareText,
      title: "Class Reservation",
    });
  };

  if (isLoading) {
    return (
      <VStack className="flex-1 justify-center items-center px-4">
        <Text className="text-lg font-dm-sans-medium text-typography-600">
          Loading class details...
        </Text>
      </VStack>
    );
  }

  // Format date from ISO string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = parse(startTime, "HH:mm:ss", new Date());
    const end = parse(endTime, "HH:mm:ss", new Date());
    const minutes = differenceInMinutes(end, start);

    return `${minutes} MINS`;
  };

  // Get intensity color and display
  const getIntensityInfo = (category: string) => {
    const intensity = category?.toLowerCase() || "low";
    switch (intensity) {
      case "high":
        return {
          color: "bg-error-500",
          label: "High",
        };
      case "medium":
        return {
          color: "bg-warning-500",
          label: "Medium",
        };
      case "low":
      default:
        return {
          color: "bg-success-500",
          label: "Low",
        };
    }
  };

  const selectedClassDate = selectedDate ?? new Date().toISOString();

  return (
    <View className="flex-1 bg-background-0">
      <StatusBar barStyle="light-content" />
      <View className="h-[300]">
        <ImageBackground
          source={{
            uri: classItem.images?.[0],
          }}
          className="h-full w-full justify-center"
          imageStyle={{
            opacity: 0.8,
            backgroundColor: "#000",
          }}
        >
          {/* Back button */}
          <Pressable
            onPress={() => router.back()}
            style={{
              position: "absolute",
              top: statusBarHeight + 16, // Status bar height + 16px padding
              left: 16,
              zIndex: 10,
            }}
            className="p-2 bg-slate-500 rounded-full"
          >
            <Icon
              as={ArrowLeft}
              size="sm"
              color="white"
              className="text-typography-900"
            />
          </Pressable>
        </ImageBackground>

        {/* Share and favorite buttons */}
        <HStack
          style={{
            position: "absolute",
            top: statusBarHeight + 16, // Status bar height + 16px padding
            right: 16,
            zIndex: 10,
          }}
          space="sm"
        >
          <Pressable
            onPress={() => setIsFavorite(!isFavorite)}
            className="p-2 bg-slate-500 rounded-full"
          >
            <Icon
              as={Heart}
              size="sm"
              color="white"
              className="text-typography-900"
            />
          </Pressable>
          <Pressable className="p-2 bg-slate-500 rounded-full">
            <Icon
              as={Share2}
              color="white"
              size="sm"
              className="text-typography-900"
            />
          </Pressable>
        </HStack>

        {/* Title overlay at bottom of image section */}
        <Box className="absolute bottom-0 left-0 right-0 p-6 pb-8">
          <Text className=" font-bold font-dm-sans-bold  bg-[#E6F9FC] mb-2  rounded-full px-2 py-1 max-w-28 text-center">
            {classItem.class_type}
          </Text>

          <Text className="text-4xl font-dm-sans-bold text-white">
            {classItem.name}
          </Text>
        </Box>
      </View>

      {/* Content card that overlays the bottom portion of the image */}
      <Box className="bg-background-0 rounded-t-3xl -mt-5 flex-1">
        <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
          <VStack space="lg" className="px-4 pt-6 pb-24">
            <Box className="bg-white rounded-xl border border-background-200 p-4 mb-4 flex">
              <HStack className="flex justify-evenly">
                <VStack>
                  <Box className="h-12 w-12 items-center justify-center">
                    <LowChartIcon />
                  </Box>
                  <Text className="text-base font-dm-sans-bold text-typography-900 text-center">
                    {getIntensityInfo(classItem.category).label}
                  </Text>
                  <Text className="text-xs font-dm-sans-regular text-typography-500">
                    INTENSITY
                  </Text>
                </VStack>

                {/* Duration */}
                <VStack>
                  <Box className="h-12 w-12 items-center justify-center">
                    <TimerIcon />
                  </Box>
                  <Text className="text-base font-dm-sans-bold text-typography-900 text-center">
                    {calculateDuration(
                      classItem.start_time,
                      classItem.end_time
                    )}
                  </Text>
                  <Text className="text-xs font-dm-sans-regular text-typography-500 text-center">
                    DURATION
                  </Text>
                </VStack>
              </HStack>
            </Box>

            {/* Date and Time */}
            <HStack className="items-center" space="xs">
              <Icon as={Calendar} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {format(
                  parseISO(selectedClassDate),
                  DATE_FORMAT.DAY_MONTH_DAY_YEAR
                )}
              </Text>
            </HStack>

            {/* Time */}
            <HStack className="items-center" space="xs">
              <Icon as={Clock} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {obtainDateFrame(classItem.start_time, classItem.end_time)}
              </Text>
            </HStack>

            <HStack className="items-center" space="xs">
              <Icon as={BookUser} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {obtainSpotsAvailable(classItem.class_type, classItem)} slots
                left
              </Text>
            </HStack>

            <HStack className="items-center" space="xs">
              <Icon as={MapPin} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {classItem.gym_name}
              </Text>
            </HStack>

            <HStack className="items-center" space="xs">
              <Icon as={DoorClosed} size="sm" className="text-typography-600" />
              <Text className="text-sm font-dm-sans-medium text-typography-600">
                {classItem.room_name}
              </Text>
            </HStack>

            <HStack
              space="sm"
              className="items-center border-t border-b border-background-200 py-4 mt-2"
            >
              <Box
                className="w-10 h-10 rounded-full items-center justify-center"
                style={{ backgroundColor }}
              >
                <Text className="text-sm font-dm-sans-bold text-white">
                  {getInitials(instructorName)}
                </Text>
              </Box>
              <VStack className="flex-1" space="xs">
                <Text className="text-xs font-dm-sans-regular text-typography-500">
                  Instructor
                </Text>
                <Text className="text-base font-dm-sans-bold text-typography-900">
                  {classItem.instructor_first_name}{" "}
                  {classItem.instructor_last_name}
                </Text>
              </VStack>
              <Pressable onPress={() => router.push("/instructor-details")}>
                <Text className="text-sm font-dm-sans-medium text-[#00697B]">
                  Read about
                </Text>
              </Pressable>
            </HStack>

            {/* Description */}
            <VStack space="sm">
              <Text className="text-md font-dm-sans-bold text-typography-900">
                Description
              </Text>
              <Text className="text-sm font-dm-sans-regular text-typography-600 leading-5">
                {classItem.description}
              </Text>
            </VStack>
          </VStack>
        </ScrollView>

        <Box className="px-6 py-6 bg-white border-t border-background-200 rounded-2xl">
          <ClassStatusButton
            status={getButtonStatus(classItem)}
            onReserve={() =>
              reserveClass({
                class_id: classItem.id,
                date: selectedDate ?? "",
                is_virtual: Boolean(classItem.is_virtual),
              })
            }
            onCancelReservation={() => {
              if (classItem.current_user_reservation) {
                cancelReservation(
                  classItem.current_user_reservation.id as number
                );
              }
            }}
            isLoading={isPending || isCancelling}
          />
        </Box>
      </Box>

      <ReservationSuccessActionsheet
        isOpen={showSuccessActionsheet}
        onClose={() => setShowSuccessActionsheet(false)}
        onMakeAnotherReservation={handleMakeAnotherReservation}
        onAddToCalendar={handleAddToCalendar}
        onShareWithFriends={handleShareWithFriends}
      />
    </View>
  );
};

export default ClassDetails;
