import { useCallback, useEffect } from 'react';
import { matchSorter } from 'match-sorter';
import { useClassesStore } from '@/stores/classes-store';
import { useClassesQuery } from '@/data/screens/classes/queries/useClassesQuery';
import { useAppointmentsQuery } from '@/data/screens/appointments/queries/useAppointmentsQuery';
import { formatDate } from '@/data/common/common.utils';
import { ClassDetailsResponse } from '@/data/screens/classes/types';
import { AppointmentType } from '@/data/screens/appointments/types';

export const useClasses = () => {
  const {
    classes,
    appointments,
    selectedTab,
    selectedDate,
    searchTerm,
    classesLoading,
    appointmentsLoading,
    classesError,
    appointmentsError,
    setClasses,
    setAppointments,
    setSelectedTab,
    setSelectedDate,
    setSearchTerm,
    setClassesLoading,
    setAppointmentsLoading,
    setClassesError,
    setAppointmentsError,
    clearSearch,
  } = useClassesStore();

  // Fetch classes data
  const {
    data: classesData = [],
    isLoading: isClassesLoading,
    error: classesQueryError,
    refetch: refetchClasses,
  } = useClassesQuery({
    date: formatDate(selectedDate),
  });

  // Fetch appointments data
  const {
    data: appointmentsData = [],
    isLoading: isAppointmentsLoading,
    error: appointmentsQueryError,
    refetch: refetchAppointments,
  } = useAppointmentsQuery({
    date: formatDate(selectedDate),
  });

  // Update store when query data changes
  useEffect(() => {
    if (classesData) {
      setClasses(classesData);
    }
  }, [classesData, setClasses]);

  useEffect(() => {
    if (appointmentsData) {
      setAppointments(appointmentsData);
    }
  }, [appointmentsData, setAppointments]);

  // Update loading states
  useEffect(() => {
    setClassesLoading(isClassesLoading);
  }, [isClassesLoading, setClassesLoading]);

  useEffect(() => {
    setAppointmentsLoading(isAppointmentsLoading);
  }, [isAppointmentsLoading, setAppointmentsLoading]);

  // Update error states
  useEffect(() => {
    setClassesError(classesQueryError as Error | null);
  }, [classesQueryError, setClassesError]);

  useEffect(() => {
    setAppointmentsError(appointmentsQueryError as Error | null);
  }, [appointmentsQueryError, setAppointmentsError]);

  // Get current data based on selected tab
  const currentData = selectedTab === 'classes' ? classes : appointments;
  const currentLoading = selectedTab === 'classes' ? classesLoading : appointmentsLoading;
  const currentError = selectedTab === 'classes' ? classesError : appointmentsError;

  // Filter data based on search term
  const filteredData = useCallback(() => {
    if (!searchTerm) return currentData;

    if (selectedTab === 'classes') {
      return matchSorter(classes, searchTerm, {
        keys: [
          'name',
          'room_name',
          'gym_name',
          'instructor_first_name',
          'instructor_last_name',
          'start_time',
        ],
      });
    } else {
      return matchSorter(appointments, searchTerm, {
        keys: ['name', 'gym_name', 'room_name', 'reservations_count'],
      });
    }
  }, [searchTerm, currentData, selectedTab, classes, appointments]);

  // Refetch current data
  const refetch = useCallback(() => {
    if (selectedTab === 'classes') {
      return refetchClasses();
    } else {
      return refetchAppointments();
    }
  }, [selectedTab, refetchClasses, refetchAppointments]);

  // Handle tab change
  const handleTabChange = useCallback(
    (tab: 'classes' | 'appointment') => {
      setSelectedTab(tab);
      clearSearch(); // Clear search when switching tabs
    },
    [setSelectedTab, clearSearch]
  );

  // Handle date change
  const handleDateChange = useCallback(
    (date: Date) => {
      setSelectedDate(date);
      clearSearch(); // Clear search when changing date
    },
    [setSelectedDate, clearSearch]
  );

  return {
    // Data
    classes,
    appointments,
    currentData,
    filteredData: filteredData(),
    
    // UI State
    selectedTab,
    selectedDate,
    searchTerm,
    
    // Loading & Error States
    isLoading: currentLoading,
    error: currentError,
    classesLoading,
    appointmentsLoading,
    classesError,
    appointmentsError,
    
    // Actions
    setSearchTerm,
    clearSearch,
    handleTabChange,
    handleDateChange,
    refetch,
    
    // Computed values
    isEmpty: !currentData.length,
    hasSearchResults: searchTerm ? filteredData().length > 0 : true,
  };
};
